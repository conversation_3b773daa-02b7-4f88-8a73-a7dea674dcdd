import React, { useState } from "react";
import {
  Bar<PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Label,
} from "recharts";

const data = {
  India: [
    {
      year: "FY-2023",
      hazardous: 4333.92,
      nonHazardous: 12848.91,
    },
    {
      year: "FY-2024",
      hazardous: 0,
      nonHazardous: 0,
    },
  ],
  Indonesia: [
    {
      year: "FY-2023",
      hazardous: 54.1,
      nonHazardous: 554.79,
    },
    {
      year: "FY-2024",
      hazardous: 0,
      nonHazardous: 0,
    },
  ],
  "United Kingdom": [
    {
      year: "FY-2023",
      hazardous: 68.37,
      nonHazardous: 78.19,
    },
    {
      year: "FY-2024",
      hazardous: 0,
      nonHazardous: 0,
    },
  ],
  Global: [
    {
      year: "FY-2023",
      hazardous: 13608.9,
      nonHazardous: 4637.66,
    },
    {
      year: "FY-2024",
      hazardous: 0,
      nonHazardous: 0,
    },
  ],
};

const LocationWaste = () => {
  const [location, setLocation] = useState("India");

  return (
    <div
      style={{
        fontFamily: "Lato",
        fontSize: "16px",
        fontWeight: 700,
        lineHeight: "19.2px",
        textAlign: "left",
        margin: "18px 10px",
      }}
    >
      <h3 style={{ fontSize: "18px", margin: "25px" }}>
        Hazardous and Non-Hazardous Waste by Location
      </h3>
      <div style={{ fontWeight: 200, fontSize: "14px", marginBottom: "10px" }}>
        Select a location to view the waste data.
      </div>
      {/* Location Selection Buttons */}
      <div style={{ marginBottom: "20px" }}>
        {Object.keys(data).map((locationName) => (
          <button
            key={locationName}
            onClick={() => setLocation(locationName)}
            style={{
              padding: "8px 16px",
              backgroundColor: location === locationName ? "#47CC" : "#F0F0F0",
              color: location === locationName ? "white" : "gray",
              border: "none",
              borderRadius: "8px",
              height: "2.5rem",
              marginRight: "2rem",
              cursor: "pointer",
            }}
          >
            {locationName}
          </button>
        ))}
      </div>
      {/* Responsive Container for Recharts */}
      <ResponsiveContainer width="100%" height={500}>
        <BarChart
          data={data[location]}
          margin={{ top: 20, right: 30, left: 20, bottom: 60 }}
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="year">
            <Label
              value="Year"
              position="bottom"
              style={{
                textAnchor: "middle",
                fill: "#333",
                fontSize: "14px",
              }}
            />
          </XAxis>
          <YAxis>
            <Label
              value="Waste (MT)"
              angle={-90}
              position="insideLeft"
              style={{
                textAnchor: "middle",
                fill: "#333",
                fontSize: "14px",
              }}
            />
          </YAxis>
          <Tooltip />
          <Legend verticalAlign="bottom" />
          {/* Hazardous and Non-Hazardous Waste Bars */}
          <Bar
            dataKey="hazardous"
            name="Hazardous Waste"
            fill="#FF9878"
            barSize={60}
          />
          <Bar
            dataKey="nonHazardous"
            name="Non-Hazardous Waste"
            fill="#F9DF7F"
            barSize={60}
          />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

export default LocationWaste;
