import React, { useState } from "react";
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Label,
} from "recharts";

const data = {
  India: [
    {
      year: "FY-2023",
      disposed: 17182.82,
    },
    {
      year: "FY-2024",
      disposed: 0,
    },
  ],
  Indonesia: [
    {
      year: "FY-2023",
      disposed: 608.9,
    },
    {
      year: "FY-2024",
      disposed: 0,
    },
  ],
  "United Kingdom": [
    {
      year: "FY-2023",
      disposed: 146.56,
    },
    {
      year: "FY-2024",
      disposed: 0,
    },
  ],
  Global: [
    {
      year: "FY-2023",
      disposed: 18246.58,
    },
    {
      year: "FY-2024",
      disposed: 0,
    },
  ],
};

const TotalWasteDisposed = () => {
  const [location, setLocation] = useState("India");

  return (
    <div
      style={{
        fontFamily: "Lato",
        fontSize: "16px",
        fontWeight: 700,
        lineHeight: "19.2px",
        textAlign: "left",
        margin: "18px 10px",
      }}
    >
      <h3 style={{ fontSize: "18px", margin: "25px" }}>
        Total Waste Disposed by Location
      </h3>
      <div style={{ fontWeight: 200, fontSize: "14px", marginBottom: "10px" }}>
        Select a location to view the total waste disposed.
      </div>
      {/* Location Selection Buttons */}
      <div style={{ marginBottom: "20px" }}>
        {Object.keys(data).map((locationName) => (
          <button
            key={locationName}
            onClick={() => setLocation(locationName)}
            style={{
              padding: "8px 16px",
              backgroundColor: location === locationName ? "#8888" : "#F0F0F0",
              color: location === locationName ? "white" : "gray",
              border: "none",
              borderRadius: "8px",
              height: "2.5rem",
              marginRight: "2rem",
              cursor: "pointer",
            }}
          >
            {locationName}
          </button>
        ))}
      </div>
      {/* Responsive Container for Recharts */}
      <ResponsiveContainer width="100%" height={500}>
        <BarChart
          data={data[location]}
          margin={{ top: 20, right: 30, left: 20, bottom: 60 }}
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="year">
            <Label
              value="Year"
              position="bottom"
              style={{
                textAnchor: "middle",
                fill: "#333",
                fontSize: "14px",
              }}
            />
          </XAxis>
          <YAxis>
            <Label
              value="Million Tons"
              angle={-90}
              position="insideLeft"
              style={{
                textAnchor: "middle",
                fill: "#333",
                fontSize: "14px",
              }}
            />
          </YAxis>
          <Tooltip />
          <Legend verticalAlign="bottom" />
          {/* Renewable Energy Bar */}
          <Bar dataKey="disposed" name="Disposed" fill="#47CC" barSize={60} />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

export default TotalWasteDisposed;
