# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.
# /build
# dependencies
/Admin_Bkp_.zip
/node_modules
/.pnp
.pnp.js
/src_copy
# testing
/coverage
/Mar_24_Adm_src.zip
# production
/src.zip
/mac_src.zip
/public.zip
/withVPChanges.zip
# misc
.DS_Store
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

npm-debug.log*
yarn-debug.log*
yarn-error.log*
