import React, { useState } from "react";
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Label,
} from "recharts";

const data = {
  India: [
    {
      year: "FY-2023",
      solar: 9349341.21,
      wind: 64605265.68,
      renewable: 11276828.61,
    },
    {
      year: "FY-2024",
      solar: 0,
      wind: 0,
      renewable: 0,
    },
  ],
  Indonesia: [
    {
      year: "FY-2023",
      solar: 0,
      wind: 0,
      renewable: 0,
    },
    {
      year: "FY-2024",
      solar: 0,
      wind: 0,
      renewable: 0,
    },
  ],
  "United Kingdom": [
    {
      year: "FY-2023",
      solar: 0,
      wind: 0,
      renewable: 0,
    },
    {
      year: "FY-2024",
      solar: 0,
      wind: 0,
      renewable: 0,
    },
  ],
  Global: [
    {
      year: "FY-2023",
      solar: 12573623,
      wind: 81003352,
      renewable: 11276829,
    },
    {
      year: "FY-2024",
      solar: 0,
      wind: 0,
      renewable: 0,
    },
  ],
};

const LocationRenewable = () => {
  const [location, setLocation] = useState("India");

  return (
    <div
      style={{
        fontFamily: "Lato",
        fontSize: "16px",
        fontWeight: 700,
        lineHeight: "19.2px",
        textAlign: "left",
        margin: "18px 10px",
      }}
    >
      <h3 style={{ fontSize: "18px", margin: "25px" }}>
        Renewable Energy Consumption by Location
      </h3>
      <div style={{ fontWeight: 200, fontSize: "14px", marginBottom: "10px" }}>
        Select a location to view the renewable energy consumption data.
      </div>
      {/* Location Selection Buttons */}
      <div style={{ marginBottom: "20px" }}>
        {Object.keys(data).map((locationName) => (
          <button
            key={locationName}
            onClick={() => setLocation(locationName)}
            style={{
              padding: "8px 16px",
              backgroundColor: location === locationName ? "#8888" : "#F0F0F0",
              color: location === locationName ? "white" : "gray",
              border: "none",
              borderRadius: "8px",
              height: "2.5rem",
              marginRight: "2rem",
              cursor: "pointer",
            }}
          >
            {locationName}
          </button>
        ))}
      </div>
      {/* Responsive Container for Recharts */}
      <ResponsiveContainer width="100%" height={500}>
        <BarChart
          data={data[location]}
          margin={{ top: 20, right: 30, left: 20, bottom: 60 }}
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="year">
            <Label
              value="Year"
              position="bottom"
              style={{
                textAnchor: "middle",
                fill: "#333",
                fontSize: "14px",
              }}
            />
          </XAxis>
          <YAxis>
            <Label
              value="Energy Consumption (KWh)"
              angle={-90}
              position="insideLeft"
              style={{
                textAnchor: "middle",
                fill: "#333",
                fontSize: "14px",
              }}
            />
          </YAxis>
          <Tooltip />
          <Legend verticalAlign="bottom" />
          {/* Renewable Energy Bar */}
          <Bar dataKey="solar" name="Solar Energy" fill="#47CC" barSize={60} />
          <Bar dataKey="wind" name="Wind Energy" fill="#88CC" barSize={60} />
          <Bar
            dataKey="renewable"
            name="Renewable Energy"
            fill="#47BB"
            barSize={60}
          />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

export default LocationRenewable;
