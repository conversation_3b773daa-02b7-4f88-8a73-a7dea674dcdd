/* Styles for the filter components */

/* Control the height of MultiSelect panels */
.p-multiselect-panel .p-multiselect-items-wrapper {
  max-height: 200px !important;
}

/* Style for selected chips in MultiSelect */
.p-multiselect.p-multiselect-chip .p-multiselect-token {
  background: #31597520;
  color: #315975;
  font-size: 12px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 6px;
  margin-right: 4px;
  margin-bottom: 2px;
}

/* Ensure the MultiSelect input doesn't grow too tall */
.p-multiselect .p-multiselect-label {
  max-height: 60px;
  overflow-y: auto;
  white-space: normal;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

/* Ensure filter dropdowns have consistent width */
.filter-dropdown {
  width: 100%;
  min-width: 180px;
}

/* Hover effect for dropdown items */
.p-multiselect-item:hover {
  background-color: #f8f9fa;
}

/* Selected item styling */
.p-multiselect-item.p-highlight {
  background-color: #31597510 !important;
  color: #315975 !important;
}
