import React, { useEffect, useState, useCallback, useRef, useMemo } from 'react';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { DateTime } from 'luxon';
import { Tag } from 'primereact/tag';
import { getDate } from '../../../../components/BGHF/helper';
import { Dialog } from 'primereact/dialog';
import DealerCompletedReport from './DealerCompletedReport';
import ActionofDealerStatus from './ActionofDealerStatus';
import { useSelector } from 'react-redux';
import { MultiSelect } from 'primereact/multiselect';
import { InputText } from 'primereact/inputtext';
import { Calendar } from 'primereact/calendar';
import APIServices from '../../../../service/APIService';
import { API } from '../../../../constants/api_url';
import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';
import AddDealerActionDialog from './components/AddDealerActionDialog';

import moment from 'moment';
import DealerSubmissionView from './DealerSubmissionView';
import Swal from 'sweetalert2';
import { Badge } from 'primereact/badge';
import { Button } from 'primereact/button';

const DealersTableCompleted = ({ data, dealerList, assessorList, globalFilter, editDealer }) => {
    const select = useSelector((state) => state.userlist);
    const userList = useSelector(state => state.userlist.userList);
    const [databk, setDatabk] = useState([])
    const [datas, setDatas] = useState([])
    const [search, setSearch] = useState('')
    const [dateFilter, setDateFilter] = useState({ start: null, end: null })
    const [submissionDialog, setSubmissionDialog] = useState(false);
    const [submissionData, setSubmissionData] = useState(null);
    const [filteredDataCount, setFilteredDataCount] = useState(0);
    const [currentFilteredData, setCurrentFilteredData] = useState([]);
    const dataTableRef = useRef(null);
    const login_data = useSelector((state) => state.user.userdetail);

    // Add filter state management for DataTable
    const [tableFilters, setTableFilters] = useState({
        dealerName: { matchMode: 'in', value: null },
        location: { matchMode: 'in', value: null },
        msiId: { matchMode: 'in', value: null },
        grade: { matchMode: 'in', value: null },
        zone: { matchMode: 'in', value: null },
        cat: { matchMode: 'in', value: null },
        calibratorName: { matchMode: 'in', value: null },
        latestSubmission: { matchMode: 'in', value: null },
        updatedScore: { matchMode: 'in', value: null },
        updatedRating: { matchMode: 'in', value: null }
    });
console.log(data)
    // Define applyFilters function before using it in useEffect
    const applyFilters = (dataToFilter, searchValue = search) => {
        // Apply search filter
        let filteredData = dataToFilter;
        if (searchValue) {
            filteredData = filteredData.filter(x =>
                x?.vendor?.dealerName?.trim().toLowerCase().includes(searchValue?.trim().toLowerCase()) ||
                x?.vendor?.code?.trim().toLowerCase().includes(searchValue?.trim().toLowerCase())
            );
        }

        // Apply date range filter
        if (dateFilter.start && dateFilter.end) {
            filteredData = filteredData.filter(rowData => {
                const dateStr = rowData?.auditStartDate
                if (!dateStr) return true;

                const itemDate = DateTime.fromISO(dateStr, { zone: 'utc' }).toJSDate();
                const startDate = moment(dateFilter.start).startOf('day').toDate();
                const endDate = moment(dateFilter.end).endOf('day').toDate();

                return itemDate >= startDate && itemDate <= endDate;
            });
        }

        // Add tableIndex property for sorting
        const indexedData = filteredData.map((item, index) => ({
            ...item,
            tableIndex: index + 1
        }));


        const finalData = indexedData.map(x => ({ ...x, grade: getRatingName(JSON.parse(x?.dealerAuditorChecklistSubmission?.score || '{}')?.overallScore || '') }));
        setDatas(finalData);

        // Don't update filteredDataCount here, let the DataTable's onFilter handle it
        // This ensures consistent count updates regardless of filter type
    };

    // Modified useEffect to only run when data actually changes, not on every render
    useEffect(() => {
        if (JSON.stringify(databk) !== JSON.stringify(data)) {
            setDatabk(data);
            applyFilters(data);
            // Don't set filteredDataCount here, let the DataTable's onFilter handle it
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [data]);

    // Separate useEffect for date filter changes
    useEffect(() => {
        if (databk.length > 0) {
            applyFilters(databk);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [dateFilter]);

    // Initialize filtered count when data changes
    useEffect(() => {
        // Set initial count when data is loaded or when search/date filters change
        // This ensures the count is always up to date
        setFilteredDataCount(datas.length);
        console.log('Data changed, setting filtered count to:', datas.length);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [datas.length]);

    const [dealerSelfSubmissions, setDealerSelfSubmissions] = useState([]);

    useEffect(() => {
        const fetchSubmissions = async () => {
            try {
                const res = await APIServices.get(API.DealerSelfSubmission);
                setDealerSelfSubmissions(res?.data || []);
            } catch (error) {
                console.error('Error fetching DealerSelfSubmission:', error);
            }
        };

        fetchSubmissions();
    }, []);

    // DealerChecklistSubmission_Edit
    const zonalOfficeList = [{ name: "Central", value: 1 }, { name: "East", value: 2 }, { name: "North", value: 3 }, { name: "South", value: 9 }, { name: "South1", value: 4 }, { name: "South2", value: 5 }, { name: "West", value: 8 }, { name: "West1", value: 6 }, { name: "West2", value: 7 }, { name: "TN", value: 10 }, { name: "North1", value: 11 }, { name: "North2", value: 12 }]
    const [reportdialog, setReportDialog] = useState(false)
    const [selectedAudit, setSelectedAudit] = useState(null)
    const [actionReportData, setActionReportData] = useState([])
    const [actionStatusReport, setActionStatusReport] = useState(false)
    const [addActionDialogVisible, setAddActionDialogVisible] = useState(false)
    const [selectedDealer, setSelectedDealer] = useState(null)
    const searchFn = (e) => {
        let val = e.target.value
        setSearch(val)
        applyFilters(databk, val)
    }
    const statusOfActionsTemplate = (rowData) => {
        console.log(rowData.actions, 'Actions Data')
        if (!Array.isArray(rowData.actions)) {
            return <span className="redBox">0/0</span>;
        }

        // Group actions by trackId
        const actionsByTrackId = {};
        rowData.actions.forEach(action => {
            const trackId = action.trackId || 'untracked'; // Use 'untracked' as default if trackId is missing
            if (!actionsByTrackId[trackId]) {
                actionsByTrackId[trackId] = [];
            }
            actionsByTrackId[trackId].push(action);
        });

        // Get unique actions by trackId (one per trackId)
        const uniqueActions = Object.values(actionsByTrackId).map(group => group[0]);
        const totalActions = uniqueActions.length;

        if (totalActions === 0) {
            return <span className="redBox">0/0</span>;
        }

        // Count completed actions
        const completedCount = Object.values(actionsByTrackId).filter(group => {
            // Check if all "Checklist Submission" actions are completed
            const submissionActions = group.filter(action => action.actionType === "Checklist Submission");
            const allSubmissionActionsCompleted = submissionActions.length === 0 ||
                submissionActions.every(action => action.status === 'Completed');

            // Check if all "Checklist Submission Review" actions are completed
            const reviewActions = group.filter(action => action.actionType === "Checklist Submission Review");
            const allReviewActionsCompleted = reviewActions.length === 0 ||
                reviewActions.every(action => action.status === 'Completed');

            // Group is considered completed only if both conditions are met
            return allSubmissionActionsCompleted && allReviewActionsCompleted;
        }).length;

        const ratio = `${completedCount}/${totalActions}`;

        // Determine color based on completion status
        let className = "redBox"; // Default red
        if (completedCount === totalActions && totalActions > 0) {
            className = "greenBox"; // All completed
        } else if (completedCount > 0) {
            className = "orangeBox"; // Some completed
        }

        return <span className={className} style={{ cursor: 'pointer' }} onClick={() => dealerActionReport(rowData)}>{ratio}</span>;
    };
    const sendReportTemplate = (rowData) => {
        const show = rowData?.dealerAuditorChecklistSubmission?.type === 2 || false
        const count = Array.isArray(rowData?.dealerAuditorChecklistSubmission?.reportMailStatus) ? rowData?.dealerAuditorChecklistSubmission?.reportMailStatus?.length  : 0
        return show ? <div>
             <i className='pi pi-envelope p-overlay-badge' style={{fontSize:'1.5rem'}} onClick={() => {

            // Show confirmation dialog
            Swal.fire({
                title: 'Send Report',
                text: 'Are you sure you want to send this report?',
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, send it!'
            }).then(async (result) => {
                if (result.isConfirmed) {
                    // User confirmed, proceed with sending the report
                    const mailStatus = await APIServices.post(API.SendReportToDealer(rowData?.dealerAuditorChecklistSubmission.id), { requestId: login_data.id })

                    // Show success/error message
                    Swal.fire({
                        position: 'center',
                        title: mailStatus?.data?.message || 'Something went wrong, try again',
                        showConfirmButton: false,
                        timer: 1500
                    })
                }
            })

        }} >

    <Badge value={count}></Badge>
</i>


            </div> : <div>NA</div>
    }
    const calibrationIdBodyTemplate = (rowData) => {
        console.log(rowData, 'ID Data')
        return (
            <a href="#" onClick={(e) => { e.preventDefault(); setSelectedAudit(rowData); setReportDialog(true); }}>
                {'MSI-' + (rowData?.vendor?.code || 'NA') + '-' + DateTime.fromISO(rowData.created_on, { zone: 'utc' }).toLocal().toFormat('ddMMyyyy')}
            </a>
        );
    };
    
    // const dontUSETHISPlease = (rowData)=>{
    //     return (
    //         <div>
              
    //                 <Button

    //                    label={  'MSI-' + (rowData?.vendor?.code || 'NA') + '-' + DateTime.fromISO(rowData.created_on, { zone: 'utc' }).toLocal().toFormat('ddMMyyyy')}
    //                     className='mandatory'
    //                     onClick={() => { APIServices.delete(API.DeleteCompleteDealerCalibrationData(rowData.id))}}
    //                 /> :
    //                 'NA'
                
    //         </div>
    //     )
    // }

    const calibrationScoreTemplate = (rowData) => {
        return JSON.parse(rowData?.dealerAuditorChecklistSubmission?.score || '{overallScore:-}')?.overallScore
    }

    const sortCalibrationScore = (e) => {
        if (e.order === 1) { // ascending
            return e.data.sort((a, b) => {
                const scoreA = getCalibrationScore(a);
                const scoreB = getCalibrationScore(b);

                if (scoreA === '-' && scoreB === '-') return 0;
                if (scoreA === '-') return 1; // '-' values at the end for ascending
                if (scoreB === '-') return -1;

                return scoreA - scoreB;
            });
        } else { // descending
            return e.data.sort((a, b) => {
                const scoreA = getCalibrationScore(a);
                const scoreB = getCalibrationScore(b);

                if (scoreA === '-' && scoreB === '-') return 0;
                if (scoreA === '-') return 1; // '-' values at the end for descending too
                if (scoreB === '-') return -1;

                return scoreB - scoreA;
            });
        }
    };

    const getCalibrationScore = (rowData) => {
        return JSON.parse(rowData?.dealerAuditorChecklistSubmission?.score || '{overallScore:"-"}')?.overallScore;
    };
    const dealerType = [{ name: 'Authorized Main Dealer', value: 1 }, { name: 'Authorized Dealer', value: 2 }, { name: 'Authorized Parts Stockist (APS)', value: 3 }, { name: 'Area Office', value: 4 }]

    const calibiratorTemplate = (rowData) => {
        if (rowData?.dealerAuditorChecklistSubmission) {
            let findId = rowData?.dealerAuditorChecklistSubmission?.modified_by || rowData?.dealerAuditorChecklistSubmission?.created_by || null;
            if (findId && assessorList && assessorList.length > 0) {
                const assessor = assessorList.find(i => i.id === findId);
                return assessor?.information?.empname || 'Not Found';
            }
            return 'Not Assigned';
        } else {
            return 'Not Assigned';
        }
    }

    const nameTemplate = (rowData) => {
        console.log(rowData)

        return (

            <div  >{rowData?.vendor?.dealerName || 'NA'}</div>


        );
    };

    const selfAssessmentMonthTemplate = (rowData) => {
        if (!Array.isArray(dealerSelfSubmissions)) return 'NA';

        const matched = dealerSelfSubmissions
            .filter(sub => sub.dealerId === rowData.dealerId)
            .sort((a, b) => {
                const dateA = moment(a.reporting_period?.[0], 'MM-YYYY');
                const dateB = moment(b.reporting_period?.[0], 'MM-YYYY');
                return dateB - dateA;
            });

        if (matched.length === 0 || !matched[0]?.reporting_period?.[0]) return 'NA';

        return moment(matched[0].reporting_period[0], 'MM-YYYY').format('MMMM YYYY');
    };
    const getLastSubmissionMonth = (rowData) => {

        console.log(rowData)
        const matched = dealerSelfSubmissions
            .filter(sub => sub.dealerId === rowData.dealerId)
            .sort((a, b) => {
                const dateA = moment(a.reporting_period?.[0], 'MM-YYYY');
                const dateB = moment(b.reporting_period?.[0], 'MM-YYYY');
                return dateB - dateA;
            });
        if (matched.length === 0 || !matched[0]?.reporting_period?.[0]) return '';
        console.log(DateTime.fromFormat(matched[0].reporting_period[0], 'LL-yyyy'))
        return DateTime.fromFormat(matched[0].reporting_period[0], 'LL-yyyy').toFormat('LLLL yyyy');

    };
    const selfAssessmentScoreTemplate = (rowData) => {
        const matched = dealerSelfSubmissions
            .filter(sub => sub.dealerId === rowData.dealerId)
            .sort((a, b) => {
                const [aMonth, aYear] = a.reportingPeriod?.[0]?.split('-') || [];
                const [bMonth, bYear] = b.reportingPeriod?.[0]?.split('-') || [];
                const dateA = DateTime.fromFormat(`${aMonth}-${aYear}`, 'MM-yyyy');
                const dateB = DateTime.fromFormat(`${bMonth}-${bYear}`, 'MM-yyyy');
                return dateB.toMillis() - dateA.toMillis(); // descending
            });

        if (matched.length > 0) {
            try {
                const scoreObj = JSON.parse(matched[0].score || '{}');
                return scoreObj.overallScore ?? 'NA';
            } catch (e) {
                return 'NA';
            }
        }

        return 'NA';
    };
    const getRatingName = (score) => {
        if (!score || score === '-' || score === 'NA') return 'NA';
        score = parseFloat(score);
        if (score >= 85) return 'Platinum';
        if (score > 70) return 'Gold';
        if (score > 55) return 'Silver';
        return 'Not Met';
    };

    const sortSelfAssessmentScore = (e) => {
        if (e.order === 1) { // ascending
            return e.data.sort((a, b) => {
                const scoreA = getSelfAssessmentScore(a);
                const scoreB = getSelfAssessmentScore(b);

                if (scoreA === 'NA' && scoreB === 'NA') return 0;
                if (scoreA === 'NA') return 1; // 'NA' values at the end for ascending
                if (scoreB === 'NA') return -1;

                return scoreA - scoreB;
            });
        } else { // descending
            return e.data.sort((a, b) => {
                const scoreA = getSelfAssessmentScore(a);
                const scoreB = getSelfAssessmentScore(b);

                if (scoreA === 'NA' && scoreB === 'NA') return 0;
                if (scoreA === 'NA') return 1; // 'NA' values at the end for descending too
                if (scoreB === 'NA') return -1;

                return scoreB - scoreA;
            });
        }
    };

    const getSelfAssessmentScore = (rowData) => {
        const matched = dealerSelfSubmissions
            .filter(sub => sub.dealerId === rowData.dealerId)
            .sort((a, b) => {
                const [aMonth, aYear] = a.reportingPeriod?.[0]?.split('-') || [];
                const [bMonth, bYear] = b.reportingPeriod?.[0]?.split('-') || [];
                const dateA = DateTime.fromFormat(`${aMonth}-${aYear}`, 'MM-yyyy');
                const dateB = DateTime.fromFormat(`${bMonth}-${bYear}`, 'MM-yyyy');
                return dateB.toMillis() - dateA.toMillis(); // descending
            });

        if (matched.length > 0) {
            try {
                const scoreObj = JSON.parse(matched[0].score || '{}');
                return scoreObj.overallScore ?? 'NA';
            } catch (e) {
                return 'NA';
            }
        }

        return 'NA';
    };


    const dealerActionReport = (rowData) => {
        setActionReportData(rowData)
        setActionStatusReport(true)
    }

    // Add Action template
    const addActionTemplate = (rowData) => {
        return (
            <div className="flex justify-content-center">
                <button
                    className="p-button p-button-rounded p-button-text"
                    onClick={() => {
                        setSelectedDealer(rowData);
                        setAddActionDialogVisible(true);
                    }}
                >
                    <i className="pi pi-plus-circle" style={{ color: '#315975' }}></i>
                </button>
            </div>
        );
    };

    // Handle action added callback
    const handleActionAdded = (updatedDealer) => {
        // Update the data in the table
        const updatedData = datas.map(item =>
            item.id === updatedDealer.id ? updatedDealer : item
        );
        setDatas(updatedData);

        // Also update the backup data
        const updatedBackupData = databk.map(item =>
            item.id === updatedDealer.id ? updatedDealer : item
        );
        setDatabk(updatedBackupData);
    };

    // Use useCallback to prevent unnecessary re-renders when opening dialogs
    const handleViewSubmission = useCallback((rowData) => {
        try {
            const parsed = JSON.parse(rowData?.dealerAuditorChecklistSubmission?.response || '{}');

            // Prepare dealer info for export
            const dealerInfo = {
                calibrationId: 'MSI-' + (rowData?.vendor?.code || 'NA') + '-' + DateTime.fromISO(rowData.created_on, { zone: 'utc' }).toLocal().toFormat('ddMMyyyy'),
                dealerName: rowData?.vendor?.dealerName || 'NA',
                location: rowData?.vendor?.dealerLocation || 'NA',
                zone: zonalOfficeList.find(x => x.value === rowData?.vendor?.dealerZone)?.name || 'NA',
                category: dealerType.find(x => x.value === rowData.vendor?.dealerCategory)?.name || 'NA',
                selfAssessmentMonth: selfAssessmentMonthTemplate(rowData),
                selfAssessmentScore: selfAssessmentScoreTemplate(rowData),
                calibrationScore: calibrationScoreTemplate(rowData),
                msiRating: getRatingName(calibrationScoreTemplate(rowData)),
                updatedScore: updatedScoreRowTemplate(rowData),
                updatedMsiRating: getRatingName(updatedScoreRowTemplate(rowData)),
                calibrationSubmissionDate: rowData.auditStartDate ? DateTime.fromISO(rowData.auditStartDate, { zone: 'utc' }).toFormat('dd-MM-yyyy') : 'NA',
                calibrationTeamMember: calibiratorTemplate(rowData),
                statusOfActions: (() => {
                    if (!Array.isArray(rowData.actions)) return '0/0';

                    // Group actions by trackId
                    const actionsByTrackId = {};
                    rowData.actions.forEach(action => {
                        const trackId = action.trackId || 'untracked';
                        if (!actionsByTrackId[trackId]) {
                            actionsByTrackId[trackId] = [];
                        }
                        actionsByTrackId[trackId].push(action);
                    });

                    // Get total unique actions by trackId
                    const totalActions = Object.keys(actionsByTrackId).length;

                    if (totalActions === 0) return '0/0';

                    // Count completed actions
                    const completedCount = Object.values(actionsByTrackId).filter(group => {
                        // Check if all "Checklist Submission" actions are completed
                        const submissionActions = group.filter(action => action.actionType === "Checklist Submission");
                        const allSubmissionActionsCompleted = submissionActions.length === 0 ||
                            submissionActions.every(action => action.status === 'Completed');

                        // Check if all "Checklist Submission Review" actions are completed
                        const reviewActions = group.filter(action => action.actionType === "Checklist Submission Review");
                        const allReviewActionsCompleted = reviewActions.length === 0 ||
                            reviewActions.every(action => action.status === 'Completed');

                        // Group is considered completed only if both conditions are met
                        return allSubmissionActionsCompleted && allReviewActionsCompleted;
                    }).length;

                    return `${completedCount}/${totalActions}`;
                })()
            };

            setSubmissionData(parsed);
            setSelectedAudit(dealerInfo);
            setSubmissionDialog(true);
        } catch (err) {
            console.error('Invalid JSON in response', err);
            alert('Invalid submission data');
        }
    }, [dealerType, zonalOfficeList, dealerSelfSubmissions]);

    const contact1Template = (rowData) => {
        return (

            rowData.dealer?.information?.principalName || 'NA'

        );
    };
    const contact2Template = (rowData) => {
        return (

            rowData.dealer?.information?.workersmName || 'NA'

        );
    };
    const actionBodytemplate = (rowData) => {
        return (
            <div>
                <button className="btn btn-sm btn-primary" onClick={() => { editDealer(rowData) }}>Edit</button>
            </div>
        )
    }
    const contact3Template = (rowData) => {
        return (

            rowData.dealer?.information?.salesmName || 'NA'

        );
    };
    const locationTemplate = (rowData) => {
        return (

            rowData.vendor?.dealerLocation || 'NA'

        );
    };
    const zoneTemplate = (rowData) => {
        return (

            zonalOfficeList.find(x => x.value === rowData?.vendor?.dealerZone)?.name || 'NA'

        );
    };
    const categoryTemplate = (rowData) => {
        return (
            dealerType.find(x => x.value === rowData.vendor?.dealerCategory)?.name || 'NA'

        );
    };
    const statusTemplate = (rowData) => {
        return rowData?.dealerAuditorChecklistSubmission?.type === 1 ? <Tag className='status-tag-green' >Completed</Tag> : rowData?.dealerAuditorChecklistSubmission?.type === 0 ? <Tag className='status-tag-orange' >In Progress</Tag> : <Tag className='status-tag-gray' >Not Started</Tag>
    }
    // const statusOfActionsTemplate = (rowData) => {
    //     return <span>{rowData.statusOfActions}</span>;
    // };

    const handleCalibrationClick = (rowData) => {
        // Logic when calibration ID is clicked
        alert('Clicked on: ' + rowData.calibrationId);
    };
    const ratingTemplate = (rowData) => {
        const score = JSON.parse(rowData?.dealerAuditorChecklistSubmission?.score || '{overallScore:-}')?.overallScore
        return (

            <div style={{ width: score > 55 ? 50 : 80 }}>
                {score != null ? score >= 85 ? <img width={'100%'} alt="Platinum Rating" src={require('../../../../assets/images/report/valuechain/platinum_rating.png').default} /> : score > 70 ? <img width={'100%'} alt="Gold Rating" src={require('../../../../assets/images/report/valuechain/gold_rating.png').default} /> : score > 55 ? <img width={'100%'} alt="Silver Rating" src={require('../../../../assets/images/report/valuechain/silver_rating.png').default} /> : "Not Met" : "NA"}

            </div>
        )
    }

    // Template for updated score
    const updatedScoreRowTemplate = (rowData) => {
        try {
            const parsed = JSON.parse(rowData?.dealerAuditorChecklistSubmission?.updatedScore || '{"overallScore": null}');

                console.log('score', rowData?.dealerAuditorChecklistSubmission)

            return parsed?.overallScore ?? 'NA';
        } catch (e) {
            return 'NA'; // fallback in case of malformed JSON
        }
    };


    // Template for updated MSI rating
    const updatedRatingRowTemplate = (rowData) => {
        let score = null;
        try {
            const parsed = JSON.parse(rowData?.dealerAuditorChecklistSubmission?.updatedScore || '{"overallScore": null}');
            score = parsed?.overallScore;

        } catch (e) {
            score = null;
        }

        return (
            <div style={{ width: score > 55 ? 50 : 80 }}>
                {score != null ? (
                    score >= 85 ? (
                        <img
                            width={'100%'}
                            alt="Platinum Rating"
                            src={require('../../../../assets/images/report/valuechain/platinum_rating.png').default}
                        />
                    ) : score > 70 ? (
                        <img
                            width={'100%'}
                            alt="Gold Rating"
                            src={require('../../../../assets/images/report/valuechain/gold_rating.png').default}
                        />
                    ) : score > 55 ? (
                        <img
                            width={'100%'}
                            alt="Silver Rating"
                            src={require('../../../../assets/images/report/valuechain/silver_rating.png').default}
                        />
                    ) : (
                        'Not Met'
                    )
                ) : (
                    'NA'
                )}
            </div>
        );
    };

    // Template for approved by
    const approvedByTemplate = (rowData) => {
        const approvedById = rowData?.dealerAuditorChecklistSubmission?.approved_by;
        if (!approvedById) return 'NA';

        const index = userList.findIndex((i) => i.id === approvedById);
        return index !== -1 ? userList[index].information?.empname || 'NA' : 'NA';
    };

    // Template for approved date
    const approvedDateTemplate = (rowData) => {
        const approvedOn = rowData?.dealerAuditorChecklistSubmission?.approved_on;
        if (!approvedOn) return 'NA';

        return moment(approvedOn).format('DD/MM/YYYY');
    };

    // Sort function for updated score
    const sortUpdatedScore = (e) => {
        if (e.order === 1) { // ascending
            return e.data.sort((a, b) => {
                try {
                    const scoreA = JSON.parse(a?.dealerAuditorChecklistSubmission?.updatedScore || '{overallScore:"-"}')?.overallScore;
                    const scoreB = JSON.parse(b?.dealerAuditorChecklistSubmission?.updatedScore || '{overallScore:"-"}')?.overallScore;

                    if (scoreA === '-' && scoreB === '-') return 0;
                    if (scoreA === '-') return 1; // '-' values at the end for ascending
                    if (scoreB === '-') return -1;

                    return parseFloat(scoreA) - parseFloat(scoreB);
                } catch (error) {
                    console.error('Error in sortUpdatedScore:', error);
                    return 0;
                }
            });
        } else { // descending
            return e.data.sort((a, b) => {
                try {
                    const scoreA = JSON.parse(a?.dealerAuditorChecklistSubmission?.updatedScore || '{overallScore:"-"}')?.overallScore;
                    const scoreB = JSON.parse(b?.dealerAuditorChecklistSubmission?.updatedScore || '{overallScore:"-"}')?.overallScore;

                    if (scoreA === '-' && scoreB === '-') return 0;
                    if (scoreA === '-') return 1; // '-' values at the end for descending too
                    if (scoreB === '-') return -1;

                    return parseFloat(scoreB) - parseFloat(scoreA);
                } catch (error) {
                    console.error('Error in sortUpdatedScore:', error);
                    return 0;
                }
            });
        }
    };
    const sortAuditStartDate = (e) => {
        console.log(e.data);
        if (e.order === 1) {
            return e.data.sort((a, b) => {
                const date1 = a?.auditStartDate || null
                const date2 = b?.auditStartDate || null
                console.log(date1, date2)
                let dateA = DateTime.fromISO(date1, { zone: 'utc' });
                let dateB = DateTime.fromISO(date2, { zone: 'utc' });


                // Compare the parsed dates
                if (dateA < dateB) return -1;
                if (dateA > dateB) return 1;
                return 0;
            });
        } else {
            return e.data.sort((a, b) => {
                const date1 = a?.auditStartDate || null
                const date2 = b?.auditStartDate || null

                let dateA = DateTime.fromISO(date1, { zone: 'utc' });
                let dateB = DateTime.fromISO(date2, { zone: 'utc' });
                if (dateA > dateB) return -1;
                if (dateA < dateB) return 1;
                return 0;
            });
        }
    };



    const RowFilterTemplate = (options, obj) => {
        console.log(data)
        return (
            <MultiSelect
                value={options.value}
                options={Array.from(new Set(datas.map((i) => i[obj]))).filter(x => x)}
                onChange={(e) => options.filterCallback(e.value)}
                placeholder="Any"
                filter
                panelClassName='hidefilter'
                className="p-column-filter"
                maxSelectedLabels={1}
                style={{ minWidth: "14rem" }}
            />
        );
    };
    const RowFilterTemplate_ = (options, obj) => {
        console.log(data)
        return (
            <MultiSelect
                value={options.value}
                options={Array.from(new Set(datas.map(x => ({ ...x, calibratorName: calibiratorTemplate(x), latestSubmission: getLastSubmissionMonth(x) })).map((i) => i[obj]))).filter(x => x)}
                onChange={(e) => options.filterCallback(e.value)}
                placeholder="Any"
                filter
                panelClassName='hidefilter'
                className="p-column-filter"
                maxSelectedLabels={1}
                style={{ minWidth: "14rem" }}
            />
        );
    };

    // Specialized filter template for MSI Rating column that includes all possible rating values
    const MSIRatingFilterTemplate = (options) => {
        // Define all possible MSI rating values
        const allPossibleRatings = [
            { label: 'Platinum', value: 'Platinum' },
            { label: 'Gold', value: 'Gold' },
            { label: 'Silver', value: 'Silver' },
            { label: 'Not Met', value: 'Not Met' },
            { label: 'NA', value: 'NA' }
        ];

        return (
            <MultiSelect
                value={options.value}
                options={allPossibleRatings}
                onChange={(e) => options.filterCallback(e.value)}
                placeholder="Any"
                filter
                panelClassName='hidefilter'
                className="p-column-filter"
                maxSelectedLabels={1}
                style={{ minWidth: "14rem" }}
            />
        );
    };
    // Sort function for S.No column
    const sortIndexColumn = (e) => {
        const { data, order } = e;

        // Create a new array with the current data and add an index property
        const indexedData = data.map((item, index) => ({
            ...item,
            tableIndex: index + 1
        }));

        // Sort based on the index
        if (order === 1) { // ascending
            return indexedData.sort((a, b) => a.tableIndex - b.tableIndex);
        } else { // descending
            return indexedData.sort((a, b) => b.tableIndex - a.tableIndex);
        }
    };


    const clearDateFilter = () => {
        setDateFilter({ start: null, end: null });
    };

    const exportExcel = () => {
        if (!datas || datas.length === 0) {
            alert('No data to export.');
            return;
        }

        const exportData = datas.map((item) => ({
            'S.No': item.tableIndex || '',
            'Calibration ID': item.vendor?.code ? `MSI-${item.vendor.code}-${DateTime.fromISO(item.created_on, { zone: 'utc' }).toLocal().toFormat('ddMMyyyy')}` : 'NA',
            'Dealer Name': item.vendor?.dealerName || 'NA',
            'Location': item.vendor?.dealerLocation || 'NA',
            'Zone': zonalOfficeList.find(x => x.value === item.vendor?.dealerZone)?.name || 'NA',
            'Category': dealerType.find(x => x.value === item.vendor?.dealerCategory)?.name || 'NA',
            'Latest Self-assessment Month': selfAssessmentMonthTemplate(item),
            'MSI Self-assessment Score': selfAssessmentScoreTemplate(item),
            'MSI Calibration Score': calibrationScoreTemplate(item),
            'MSI Rating': getRatingName(calibrationScoreTemplate(item)),
            'Updated Score': updatedScoreRowTemplate(item),
            'Updated MSI Rating': getRatingName(updatedScoreRowTemplate(item)),
            'Calibration Submission Date': item.auditStartDate ? DateTime.fromISO(item.auditStartDate, { zone: 'utc' }).toFormat('dd-MM-yyyy') : '',
            'Calibration Team Member': calibiratorTemplate(item),
            'Approved By': approvedByTemplate(item),
            'Approved Date': approvedDateTemplate(item),
            'Status of Actions': (() => {
                if (!Array.isArray(item.actions)) return '0/0';

                // Group actions by trackId
                const actionsByTrackId = {};
                item.actions.forEach(action => {
                    const trackId = action.trackId || 'untracked';
                    if (!actionsByTrackId[trackId]) {
                        actionsByTrackId[trackId] = [];
                    }
                    actionsByTrackId[trackId].push(action);
                });

                // Get total unique actions by trackId
                const totalActions = Object.keys(actionsByTrackId).length;

                if (totalActions === 0) return '0/0';

                // Count completed actions
                const completedCount = Object.values(actionsByTrackId).filter(group => {
                    // Check if all "Checklist Submission" actions are completed
                    const submissionActions = group.filter(action => action.actionType === "Checklist Submission");
                    const allSubmissionActionsCompleted = submissionActions.length === 0 ||
                        submissionActions.every(action => action.status === 'Completed');

                    // Check if all "Checklist Submission Review" actions are completed
                    const reviewActions = group.filter(action => action.actionType === "Checklist Submission Review");
                    const allReviewActionsCompleted = reviewActions.length === 0 ||
                        reviewActions.every(action => action.status === 'Completed');

                    // Group is considered completed only if both conditions are met
                    return allSubmissionActionsCompleted && allReviewActionsCompleted;
                }).length;

                return `${completedCount}/${totalActions}`;
            })()
        }));

        const worksheet = XLSX.utils.json_to_sheet(exportData);
        const workbook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(workbook, worksheet, 'Dealers Completed');

        const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
        const dataBlob = new Blob([excelBuffer], { type: 'application/octet-stream' });
        saveAs(dataBlob, `Dealers_Completed_${moment().format('YYYYMMDD_HHmmss')}.xlsx`);
    };

    const selfAssessmentMonthSort = (e) => {
        console.log(e.data)
        if (e.order === 1) { // ascending
            return e.data.sort((a, b) => {
                const monthA = a.latestSubmission
                const monthB = b.latestSubmission

                if (!monthA && !monthB) return 0;
                if (!monthA) return 1;
                if (!monthB) return -1;

                const dateA = DateTime.fromFormat(monthA, 'LLLL yyyy')
                const dateB = DateTime.fromFormat(monthB, 'LLLL yyyy')
                console.log(dateA.isValid, dateB.isValid);
                if (dateA < dateB) return -1;
                if (dateA > dateB) return 1;
                return 0;
            });
        } else { // descending
            return e.data.sort((a, b) => {
                const monthA = a.latestSubmission
                const monthB = b.latestSubmission

                if (!monthA && !monthB) return 0;
                if (!monthA) return 1;
                if (!monthB) return -1;


                const dateA = DateTime.fromFormat(monthA, 'LLLL yyyy')
                const dateB = DateTime.fromFormat(monthB, 'LLLL yyyy')

                if (dateA > dateB) return -1;
                if (dateA < dateB) return 1;
                return 0;
            });
        }
    }

    // Memoize the enhanced data to prevent unnecessary re-computations
    const enhancedData = useMemo(() => {
        return datas.map(x => ({
            ...x,
            calibratorName: calibiratorTemplate(x),
            latestSubmission: getLastSubmissionMonth(x)
        }));
    }, [datas, dealerSelfSubmissions]);

    // Ensure the count is properly initialized when enhanced data changes
    useEffect(() => {
        // Only update if no filters are currently applied
        const hasActiveFilters = Object.values(tableFilters).some(filter =>
            filter.value && filter.value.length > 0
        );

        if (!hasActiveFilters && !globalFilter) {
            setFilteredDataCount(enhancedData.length);
            console.log('Enhanced data changed, updating count to:', enhancedData.length);
        }
    }, [enhancedData.length, tableFilters, globalFilter]);

    // Manual filter calculation function as a fallback
    const calculateFilteredCount = useCallback(() => {
        if (!enhancedData || enhancedData.length === 0) return 0;

        let filteredData = [...enhancedData];

        // Apply table filters
        Object.entries(tableFilters).forEach(([field, filter]) => {
            if (filter.value && filter.value.length > 0) {
                filteredData = filteredData.filter(item => {
                    const fieldValue = item[field];
                    if (fieldValue == null) return false;
                    return filter.value.includes(fieldValue);
                });
            }
        });

        // Apply global filter if present
        if (globalFilter && globalFilter.trim()) {
            const globalFilterLower = globalFilter.toLowerCase();
            filteredData = filteredData.filter(item => {
                return Object.values(item).some(value =>
                    value && value.toString().toLowerCase().includes(globalFilterLower)
                );
            });
        }

        return filteredData.length;
    }, [enhancedData, tableFilters, globalFilter]);

    // Backup mechanism to ensure count accuracy
    useEffect(() => {
        const updateCountFromCalculation = () => {
            const calculatedCount = calculateFilteredCount();

            if (calculatedCount !== filteredDataCount) {
                console.log('🔄 Manual calculation: Updating count to:', calculatedCount);
                setFilteredDataCount(calculatedCount);
            }
        };

        // Check periodically for any missed updates
        const interval = setInterval(updateCountFromCalculation, 3000);

        return () => clearInterval(interval);
    }, [calculateFilteredCount, filteredDataCount]);

    // Also trigger manual calculation when filters change
    useEffect(() => {
        const calculatedCount = calculateFilteredCount();
        console.log('🔢 Filters changed, calculated count:', calculatedCount);

        // Small delay to allow DataTable to process first
        const timeout = setTimeout(() => {
            if (calculatedCount !== filteredDataCount) {
                console.log('🔄 Filter change: Updating count to:', calculatedCount);
                setFilteredDataCount(calculatedCount);
            }
        }, 500);

        return () => clearTimeout(timeout);
    }, [tableFilters, globalFilter, calculateFilteredCount]);
    return (<>
        <div className="col-12 flex justify-content-between align-items-center mb-3" >
            <div className="col-6 flex gap-3 align-items-center">
                <div className="flex flex-column">
                    <label className="mb-1">Calibration Date From</label>
                    <Calendar
                        value={dateFilter.start}
                        onChange={(e) => setDateFilter({ ...dateFilter, start: e.value })}
                        placeholder="Start Date"
                        dateFormat="dd-mm-yy"
                        showIcon
                    />
                </div>
                <div className="flex flex-column">
                    <label className="mb-1">To</label>
                    <Calendar
                        value={dateFilter.end}
                        onChange={(e) => setDateFilter({ ...dateFilter, end: e.value })}
                        placeholder="End Date"
                        dateFormat="dd-mm-yy"
                        showIcon
                        minDate={dateFilter.start}
                        disabled={!dateFilter.start}
                    />
                </div>
                {(dateFilter.start || dateFilter.end) && (
                    <button
                        className="btn btn-sm btn-outline-secondary align-self-end mb-1"
                        onClick={clearDateFilter}
                        style={{ height: '36px' }}
                    >
                        Clear
                    </button>
                )}
            </div>
            <div className='col-5'>
                <span className="p-input-icon-left" style={{ width: '100%' }}>
                    <i className="pi pi-search" />
                    <InputText value={search} style={{ width: '100%' }} onChange={searchFn} placeholder="Search Code/Name" />
                </span>
            </div>
        </div>
        <div className="d-flex justify-content-between align-items-center mb-3">
            <h4>Completed Dealers ({filteredDataCount})</h4>
            <button
                className="btn btn-sm btn-success"
                onClick={exportExcel}
            >
                Download Excel
            </button>
        </div>

        <DataTable
            ref={dataTableRef}
            value={enhancedData}
            paginator
            rows={10}
             rowsPerPageOptions={[10, 25, 50, 100,150,200]}
            scrollable
            scrollHeight="500px"
            filters={tableFilters}
            filterDisplay="menu"
            onPage={(e) => {
                console.log('📄 Page changed:', e);
                // Ensure count remains accurate when navigating pages
                const currentCount = calculateFilteredCount();
                if (currentCount !== filteredDataCount) {
                    console.log('🔄 Page change: Updating count to:', currentCount);
                    setFilteredDataCount(currentCount);
                }
            }}
            onFilter={(e) => {
                console.log('🔍 DataTable onFilter triggered');
                console.log('Event object:', e);

                // Create a copy of the filters object
                const cleanedFilters = { ...e.filters };

                if (cleanedFilters.hasOwnProperty('null')) {
                    delete cleanedFilters['null'];
                }

                setTableFilters(cleanedFilters);

                // Update filtered data count based on the actual filtered results
                // e.filteredValue contains ALL filtered records across all pages
                let filteredCount;

                if (e.filteredValue && Array.isArray(e.filteredValue)) {
                    filteredCount = e.filteredValue.length;
                    console.log('✅ Using e.filteredValue.length:', filteredCount);
                } else {
                    // Fallback to manual calculation
                    filteredCount = calculateFilteredCount();
                    console.log('⚠️ Fallback to manual calculation:', filteredCount);
                }

                console.log('🔢 Filter applied:', cleanedFilters);
                console.log('🔢 Final filtered count (total across all pages):', filteredCount);
                console.log('🔢 Enhanced data length:', enhancedData.length);

                // Update the count and filtered data immediately
                setFilteredDataCount(filteredCount);
                setCurrentFilteredData(e.filteredValue || enhancedData);
            }}
            globalFilter={globalFilter}
            className="mt-2 h-500">
            <Column sortable field="tableIndex" header="S.No" body={(rowData, options) => rowData.tableIndex || options.rowIndex + 1} sortFunction={sortIndexColumn} />
            <Column sortable field="msiId" header="Calibration ID" body={calibrationIdBodyTemplate} showFilterMatchModes={false}
                filter
                filterElement={(options) =>
                    RowFilterTemplate(options, "msiId")
                } ></Column>
            <Column sortable field="dealerName" header="Name" body={nameTemplate} showFilterMatchModes={false}
                filter
                filterElement={(options) =>
                    RowFilterTemplate(options, "dealerName")
                } ></Column>
            <Column sortable field="location" header="Location" body={locationTemplate} showFilterMatchModes={false}
                filter
                filterElement={(options) =>
                    RowFilterTemplate(options, "location")
                }></Column>
            <Column sortable field="zone" header="Zone" body={zoneTemplate} showFilterMatchModes={false}
                filter
                filterElement={(options) =>
                    RowFilterTemplate(options, "zone")
                }></Column>
            {/* <Column field="pinCode" header="Pin Code" body={pincodeTemplate}></Column> */}
            {/* <Column field="dealerContact" header="Dealer Contact" ></Column> */}
            {/* <Column field="pyInvoiceValue" header="Spent by TVS(in INR)" body={pyInvoiceBodyTemplate} ></Column> */}
            {/* <Column field="status" header="Status" body={statusTemplate}  ></Column> */}

            <Column sortable field="cat" header="Category" body={categoryTemplate} showFilterMatchModes={false}
                filter
                filterElement={(options) =>
                    RowFilterTemplate(options, "cat")
                }  ></Column>
            <Column sortable field="latestSubmission" header="Latest Self-assessment Month" body={selfAssessmentMonthTemplate}
                showFilterMatchModes={false}
                sortFunction={selfAssessmentMonthSort}
                filter filterElement={(options) => RowFilterTemplate_(options, 'latestSubmission')}

            />
            <Column sortable field="selfAssessmentScore" header="MSI Self-assessment Score" body={selfAssessmentScoreTemplate} sortFunction={sortSelfAssessmentScore} />

            <Column sortable field="dealerAuditorChecklistSubmission" header="MSI Calibration Score" body={calibrationScoreTemplate} sortFunction={sortCalibrationScore} ></Column>
            <Column field="grade" filter showFilterMatchModes={false}
                filterElement={(options) =>
                    MSIRatingFilterTemplate(options)
                } header="MSI Rating" body={ratingTemplate}  ></Column>

            <Column sortable field="updatedScore" header="Updated Score" body={updatedScoreRowTemplate}  ></Column>
            <Column field="updatedRating" header="Updated MSI Rating" body={updatedRatingRowTemplate} ></Column>


            {/* <Column field="selfAssessmentReportingMonth" header="Self-Assessment Reporting Month" ></Column> */}

            <Column sortable field='dealerAuditorChecklistSubmission.modified_on' header="Calibration Submission Date" sortFunction={sortAuditStartDate} body={(rowData) => getDate(rowData?.auditStartDate)} ></Column>
            <Column field="calibratorName" header="Calibration Team Member" body={calibiratorTemplate}
                showFilterMatchModes={false}
                filter
                filterElement={(options) =>
                    RowFilterTemplate_(options, "calibratorName")
                }

            ></Column>
            <Column sortable field="approved_by" header="Approved By" body={approvedByTemplate} ></Column>
            <Column sortable field="approved_on" header="Approved Date" body={approvedDateTemplate} ></Column>
            <Column header="Send Report" body={sendReportTemplate} ></Column>
            <Column field="statusOfActions" header="Status of Actions" body={statusOfActionsTemplate}></Column>
            <Column header="Add Action" body={addActionTemplate}></Column>
            <Column
                header="View Submissions"
                body={(rowData) => (
                    <button
                        className="btn btn-sm btn-secondary"
                        onClick={() => handleViewSubmission(rowData)}
                    >
                        View
                    </button>
                )}
            />

            {/*
            <Column field="statusOfActions" header="Status of Actions" body={statusOfActionsTemplate}></Column>
            <Column field="zonalManager" header="Zonal Manager" body={contact1Template}></Column>
            <Column field="areaManager" header="Area Manager" body={contact2Template}></Column>
            <Column field="territoryManager" header="Territory Manager" body={contact3Template}></Column> */}
            {/* <Column header="Action" body={actionBodytemplate}></Column> */}

        </DataTable>

        <Dialog visible={reportdialog} className="custom-dialog" style={{ width: 1200 }} onHide={() => { setReportDialog(false) }} >
            <DealerCompletedReport report={selectedAudit} />
        </Dialog>


        <Dialog visible={actionStatusReport} style={{ width: '90%' }} className="custom-dialog" onHide={() => { setActionStatusReport(false) }} >
            <ActionofDealerStatus report={actionReportData} />
        </Dialog>

        {/* Add Action Dialog */}
        <AddDealerActionDialog
            visible={addActionDialogVisible}
            onHide={() => setAddActionDialogVisible(false)}
            dealer={selectedDealer}
            onActionAdded={handleActionAdded}
        />

        <Dialog
            visible={submissionDialog}
            onHide={() => {
                setSubmissionDialog(false);
                setSelectedAudit(null); // Clear dealer info when dialog is closed
            }}
            style={{ width: '80vw' }}
            className="custom-dialog"
        >
            {submissionData ? (
                <DealerSubmissionView excelData={submissionData} dealerInfo={selectedAudit} />
            ) : (
                <p>No submission data available.</p>
            )}
        </Dialog>


    </>

    );

};


export default DealersTableCompleted;
