// Import necessary tools and components
import React from 'react';
import { render, fireEvent } from '@testing-library/react';
import { Provider } from 'react-redux';
import { BrowserRouter as Router } from 'react-router-dom';
import configureStore from 'redux-mock-store';
import LoginNew from './LoginNew';
import '@testing-library/jest-dom';

// Setup a mock store
const mockStore = configureStore();
const store = mockStore({
  user: {
    userdetail: {}
  }
});

describe('LoginNew Component Tests', () => {
  it('renders correctly', () => {
    const { getByText } = render(
      <Provider store={store}>
        <Router>
          <LoginNew />
        </Router>
      </Provider>
    );
    expect(getByText('Sign in')).toBeInTheDocument();
  });

  // Additional tests...
});

